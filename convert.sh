#!/bin/bash

# Exit immediately if a command exits with a non-zero status.
set -e

# --- 1. Validate Input ---
# Check if the user provided exactly one argument.
if [ "$#" -ne 1 ]; then
    echo "Usage: $0 /path/to/your/book.txt"
    exit 1
fi

INPUT_BOOK_PATH="$1"

# Check if the provided path is actually a file.
if [ ! -f "$INPUT_BOOK_PATH" ]; then
    echo "Error: File not found at '$INPUT_BOOK_PATH'"
    exit 1
fi

# --- 2. Derive Paths ---
# Get the absolute path to the directory containing the book.
HOST_WORK_DIR=$(dirname "$(realpath "$INPUT_BOOK_PATH")")
# Get just the filename of the book.
BOOK_FILENAME=$(basename "$INPUT_BOOK_PATH")
# Get the filename without the extension, used for the output folder.
BOOK_BASENAME="${BOOK_FILENAME%.*}"

echo "-------------------------------------"
echo "Starting Audiobook Conversion"
echo "Host Directory: $HOST_WORK_DIR"
echo "Book Filename:  $BOOK_FILENAME"
echo "Output Folder:  $BOOK_BASENAME"
echo "-------------------------------------"

# --- 3. Run Docker Compose ---
# Navigate to the directory where this script and docker-compose.yml are located.
cd "$(dirname "$0")"

# Execute docker-compose, dynamically providing the volume and command.
docker-compose run --rm \
    -v "$HOST_WORK_DIR:/data" \
    piper-tts \
    "/data/$BOOK_FILENAME" "/data/$BOOK_BASENAME"
