# --- Stage 1: The Builder ---
FROM rocm/dev-ubuntu-22.04 AS builder

# Declare the build arguments
ARG HTTP_PROXY
ARG HTTPS_PROXY
ARG NO_PROXY

# Set the environment variables
ENV http_proxy=${HTTP_PROXY}
ENV https_proxy=${HTTPS_PROXY}
ENV no_proxy=${NO_PROXY}

# Avoid interactive prompts
ENV DEBIAN_FRONTEND=noninteractive

# Create a dedicated proxy configuration for APT
RUN echo "Acquire::http::Proxy \"${HTTP_PROXY}\";" > /etc/apt/apt.conf.d/80proxy && \
    echo "Acquire::https::Proxy \"${HTTPS_PROXY}\";" >> /etc/apt/apt.conf.d/80proxy && \
    echo "Acquire::https::Verify-Peer \"false\";" >> /etc/apt/apt.conf.d/80proxy && \
    echo "Acquire::https::Verify-Host \"false\";" >> /etc/apt/apt.conf.d/80proxy

# Install the build-time dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    git \
    wget \
    python3-pip \
    curl && \
    rm -rf /var/lib/apt/lists/*

# Install CMake 3.26+ (required for ONNX Runtime 1.16.3)
RUN wget https://github.com/Kitware/CMake/releases/download/v3.26.4/cmake-3.26.4-linux-x86_64.tar.gz && \
    tar -xzf cmake-3.26.4-linux-x86_64.tar.gz && \
    cp -r cmake-3.26.4-linux-x86_64/bin/* /usr/local/bin/ && \
    cp -r cmake-3.26.4-linux-x86_64/share/* /usr/local/share/ && \
    rm -rf cmake-3.26.4-linux-x86_64* && \
    ln -sf /usr/local/bin/cmake /usr/bin/cmake

# Explicitly configure Git to use the proxy
RUN git config --global http.proxy ${HTTP_PROXY} && \
    git config --global https.proxy ${HTTPS_PROXY} && \
    git config --global http.sslVerify false

# Set a working directory
WORKDIR /build

# Build onnxruntime-rocm from source, allowing it to run as root.
RUN git clone --recursive -b rel-1.16.3 https://github.com/microsoft/onnxruntime.git && \
    cd onnxruntime && \
    ./build.sh --config Release --update --build --parallel --use_rocm --rocm_home=/opt/rocm --build_wheel --allow_running_as_root


# --- Stage 2: The Final, Lean Image ---
FROM rocm/dev-ubuntu-22.04

# Pass the proxy arguments to this stage as well
ARG HTTP_PROXY
ARG HTTPS_PROXY
ARG NO_PROXY

ENV http_proxy=${HTTP_PROXY}
ENV https_proxy=${HTTPS_PROXY}
ENV no_proxy=${NO_PROXY}

ENV DEBIAN_FRONTEND=noninteractive

# Create the dedicated APT proxy configuration for this stage as well
RUN echo "Acquire::http::Proxy \"${HTTP_PROXY}\";" > /etc/apt/apt.conf.d/80proxy && \
    echo "Acquire::https::Proxy \"${HTTPS_PROXY}\";" >> /etc/apt/apt.conf.d/80proxy && \
    echo "Acquire::https::Verify-Peer \"false\";" >> /etc/apt/apt.conf.d/80proxy && \
    echo "Acquire::https::Verify-Host \"false\";" >> /etc/apt/apt.conf.d/80proxy

# Install only the RUNTIME dependencies
RUN apt-get update && apt-get install -y \
    ffmpeg \
    python3 \
    python3-pip \
    python3-venv && \
    rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy the compiled .whl file FROM the 'builder' stage
COPY --from=builder /build/onnxruntime/build/Linux/Release/dist/onnxruntime_rocm-*.whl .

# Configure PIP to use the proxy and trust the host
RUN pip3 install \
    --proxy ${HTTPS_PROXY} \
    --trusted-host pypi.org \
    --trusted-host files.pythonhosted.org \
    onnxruntime_rocm-*.whl && \
    rm onnxruntime_rocm-*.whl

# Install the remaining Python libraries using the same proxy flags
RUN pip3 install \
    --proxy ${HTTPS_PROXY} \
    --trusted-host pypi.org \
    --trusted-host files.pythonhosted.org \
    piper-tts==1.2.0 langdetect

# Configure WGET to ignore SSL certificate validation
RUN mkdir /models && \
    wget --no-check-certificate 'https://huggingface.co/rhasspy/piper-voices/resolve/main/en/en_US/lessac/medium/en_US-lessac-medium.onnx' -O /models/en_US-lessac-medium.onnx && \
    wget --no-check-certificate 'https://huggingface.co/rhasspy/piper-voices/resolve/main/zh/zh_CN/huayan/medium/zh_CN-huayan-medium.onnx' -O /models/zh_CN-huayan-medium.onnx

# Copy your script into the final image
COPY scripts/create_audiobook_gpu.py .

# Set the entrypoint
ENTRYPOINT ["python3", "create_audiobook_gpu.py"]
