services:
  piper-tts:
    build:
      context: .
      # This section is now more explicit.
      # It takes the variables defined in the .env file
      # and passes them as build arguments to the Dockerfile.
      args:
        HTTP_PROXY: ${HTTP_PROXY}
        HTTPS_PROXY: ${HTTPS_PROXY}
        NO_PROXY: ${NO_PROXY}
    image: piper-rocm-tts:latest
    devices:
      - /dev/kfd:/dev/kfd
      - /dev/dri:/dev/dri
