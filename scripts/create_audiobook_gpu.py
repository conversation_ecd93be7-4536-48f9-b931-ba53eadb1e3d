import os
import sys
import re
import subprocess
from langdetect import detect
from piper.voice import PiperVoice
from piper.config import PiperConfig

# --- Static Configuration (Paths are inside the Docker container) ---
EN_MODEL = "/models/en_US-lessac-medium.onnx"
ZH_MODEL = "/models/zh_CN-huayan-medium.onnx"
MIN_LENGTH = 3

def load_voice_with_gpu(model_path):
    """
    Attempts to load a PiperVoice model using the GPU (ROCm) with a CPU fallback.
    """
    providers = ['ROCmExecutionProvider', 'CPUExecutionProvider']
    print(f"Attempting to load model {os.path.basename(model_path)} with providers: {providers}")
    config = PiperConfig(providers=providers)
    voice = PiperVoice.load(model_path, config=config)
    print(f"Successfully loaded {os.path.basename(model_path)}.")
    return voice

def main():
    if len(sys.argv) != 3:
        # The paths here are what we expect INSIDE the container
        print(f"Usage: python {sys.argv[0]} <path_to_book_inside_container> <output_dir_inside_container>")
        sys.exit(1)

    BOOK_PATH, OUTPUT_DIR = sys.argv[1], sys.argv[2]
    if not os.path.isfile(BOOK_PATH):
        print(f"Error: Input file not found inside container at '{BOOK_PATH}'")
        sys.exit(1)

    book_basename = os.path.basename(BOOK_PATH)
    book_name_no_ext, _ = os.path.splitext(book_basename)
    FINAL_AUDIOBOOK_NAME = f"{book_name_no_ext}.mp3"
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    print("\nLoading TTS models into memory (attempting GPU acceleration)...")
    try:
        en_voice = load_voice_with_gpu(EN_MODEL)
        zh_voice = load_voice_with_gpu(ZH_MODEL)
    except Exception as e:
        print(f"FATAL: Error loading models: {e}")
        sys.exit(1)
    print("All models loaded successfully.")

    temp_audio_files = []
    segment_counter = 0

    with open(BOOK_PATH, 'r', encoding='utf-8') as f:
        for paragraph in f:
            paragraph = paragraph.strip()
            if len(paragraph) < MIN_LENGTH or not re.search(r'[\u4e00-\u9fa5a-zA-Z]', paragraph):
                continue

            try:
                lang = detect(paragraph)
                voice = zh_voice if lang == 'zh-cn' else en_voice
                print(f"Processing segment {segment_counter:05d}: {paragraph[:60]}...")
                temp_audio_file = os.path.join(OUTPUT_DIR, f"segment_{segment_counter:05d}.wav")

                with open(temp_audio_file, "wb") as wav_file:
                    voice.synthesize(paragraph, wav_file)

                if os.path.exists(temp_audio_file) and os.path.getsize(temp_audio_file) > 100:
                    temp_audio_files.append(temp_audio_file)
                    segment_counter += 1
                else:
                    if os.path.exists(temp_audio_file): os.remove(temp_audio_file)
            except Exception as e:
                print(f"Could not process segment: '{paragraph[:50]}...'. Error: {e}")

    if not temp_audio_files:
        print("No valid audio segments were generated.")
        sys.exit(1)

    # --- Concatenate and Convert with FFmpeg ---
    print("\nConcatenating audio segments...")
    concatenated_wav = os.path.join(OUTPUT_DIR, "combined.wav")
    file_list_path = os.path.join(OUTPUT_DIR, "file_list.txt")
    with open(file_list_path, "w", encoding='utf-8') as f:
        for audio_file in temp_audio_files:
            f.write(f"file '{audio_file}'\n")

    try:
        subprocess.run(['ffmpeg', '-y', '-f', 'concat', '-safe', '0', '-i', file_list_path, '-c', 'copy', concatenated_wav], check=True, capture_output=True)
        print("Converting to MP3...")
        final_mp3_path = os.path.join(OUTPUT_DIR, FINAL_AUDIOBOOK_NAME)
        subprocess.run(['ffmpeg', '-y', '-i', concatenated_wav, '-codec:a', 'libmp3lame', '-qscale:a', '2', final_mp3_path], check=True, capture_output=True)
    except subprocess.CalledProcessError as e:
        print(f"\n--- FFmpeg Error --- \nStderr: {e.stderr.decode('utf-8')}")
        sys.exit(1)

    # --- Cleanup ---
    for f in temp_audio_files + [concatenated_wav, file_list_path]:
        if os.path.exists(f): os.remove(f)

    print(f"\n✅ Audiobook created successfully at host path corresponding to: {final_mp3_path}")

if __name__ == "__main__":
    main()
